import assert from 'node:assert'
import type { UserSession } from '../../utils/session'
import { deleteDoc, doc, serverTimestamp } from 'firebase/firestore'
import { createError, defineEventHandler, readBody } from 'h3'
import { useFirebaseServer } from '../../firebase/init'
import { getUserSession } from '../../utils/session'

export default defineEventHandler(async (event) => {
  try {
    // Get user session
    const session: UserSession | null = await getUserSession(event)
    if (!session) {
      throw createError({
        statusCode: 401,
        message: 'Unauthorized',
      })
    }

    // Get query parameters
    const body = await readBody(event)
    const docId = body?.id
    const data = body as any
    const collection = body.collection

    if (!docId || !data || !collection) {
      throw createError({
        statusCode: 400,
        message: 'Missing required parameters',
      })
    }

    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    const { firestore } = await useFirebaseServer(idToken as string)

    // Create new research
    const res = await deleteDoc(doc(firestore, collection, docId))

    const write = {
      id: docId,
      deleted_at: serverTimestamp(),
    }

    return {
      statusCode: 200,
      data: write,
    }
  }
  catch (error: any) {
    console.error('[Research] Error:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'Failed to fetch research',
    })
  }
})
