import assert from 'node:assert'
import { doc, getDoc, updateDoc } from 'firebase/firestore'
import { createError, defineEventHandler, readBody } from 'h3'
import { useFirebaseServer } from '../../firebase/init'
import { createEmbeddings, getUserSession } from '../../utils/session'

export default defineEventHandler(async (event) => {
  // Check authentication
  const session = await getUserSession(event)
  if (!session) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    })
  }

  try {
    const body = await readBody(event)
    const { collection: collectionName, id, data, embed = [] } = body
    console.log('update: ', body)
    // Validate collection name, id, and data
    if (!collectionName) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Collection name is required',
      })
    }

    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Document ID is required',
      })
    }

    if (!data) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Data is required',
      })
    }

    console.log('token: ', session.user?.token)
    const idToken = session.user?.token?.idToken || session.user?.token?.accessToken
    console.log('idToken extracted: ', idToken ? 'Token found' : 'No token found')
    const { firestore } = await useFirebaseServer(idToken as string)

    // Get document reference
    const docRef = doc(firestore, collectionName, id)

    // Check if document exists
    const docSnapshot = await getDoc(docRef)
    if (!docSnapshot.exists()) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Document not found',
      })
    }

    // Check ownership if userId is present in the document
    const docData = docSnapshot.data()
    if (docData.userId && session.user?.id && docData.userId !== session.user.id) {
      throw createError({
        statusCode: 403,
        statusMessage: 'You do not have permission to update this document',
      })
    }

    // Generate embeddings for specified fields
    const dataWithEmbeddings = { ...data }

    // Generate embeddings if fields are specified using the imported createEmbeddings
    if (embed && Array.isArray(embed) && embed.length > 0) {
      const embeddings = await createEmbeddings(data, embed)

      if (embeddings) {
        dataWithEmbeddings.embedding = embeddings
      }
    }

    // Add updated timestamp
    dataWithEmbeddings.updatedAt = new Date().toISOString()

    // Update the document
    console.log('Attempting to update document:', { collection: collectionName, id, data: dataWithEmbeddings })
    const updateDocResult = await updateDoc(docRef, dataWithEmbeddings)
    console.log('updateDoc call finished.')
    console.log('-------------', updateDocResult)
    // Return updated data with ID
    const updatedData = {
      id,
      ...docData,
      ...dataWithEmbeddings,
    }
    return {
      statusCode: 200,
      data: updatedData,
    }
  }
  catch (error: any) {
    console.error('Data update error:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || 'Error updating data',
    })
  }
})
