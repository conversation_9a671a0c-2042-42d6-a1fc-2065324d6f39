rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    
    // Check if user is a member of workspace
    function isWorkspaceMember(workspaceId) {
      return isAuthenticated() && 
        exists(/databases/$(database)/documents/workspace_members/$(workspaceId + '_' + request.auth.uid));
    }
    
    // Get workspace member document
    function getWorkspaceMember(workspaceId) {
      return get(/databases/$(database)/documents/workspace_members/$(workspaceId + '_' + request.auth.uid));
    }
    
    // Check if user has specific role in workspace
    function hasWorkspaceRole(workspaceId, roles) {
      return isAuthenticated() && 
        isWorkspaceMember(workspaceId) &&
        getWorkspaceMember(workspaceId).data.role in roles;
    }
    
    function isWorkspaceAdmin(workspaceId) {
      return hasWorkspaceRole(workspaceId, ['owner', 'admin']);
    }
    
    // Test collection - allow authenticated users to read/write their own test documents
    match /test/{document} {
      allow read, write: if request.auth != null;
    }
    
    // Frontend test writes collection - for testing Firebase connectivity
    match /frontend-test-writes/{document} {
      allow read, write: if request.auth != null;
    }
    
    // Leads collection - for storing project idea submissions
    match /leads/{document} {
      // Allow public creation of leads (for chatbot functionality)
      allow create: if true;
      
      // Allow users to read their own leads (using email verification)
      allow read: if request.auth != null 
        && request.auth.token.email == resource.data.contactEmail;
      
      // Allow admin users to read all leads (implement admin check later)
      allow read: if request.auth != null 
        && exists(/databases/$(database)/documents/users/$(request.auth.uid)) 
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
      
      // Prevent updates and deletes for data integrity
      allow update, delete: if false;
    }
    
    // Users collection - stores user authentication data
    match /users/{userId} {
      allow read: if isOwner(userId);
      allow create: if isOwner(userId);
      allow update: if isOwner(userId);
      allow delete: if false; // Soft delete only through update
    }
    
    // Profiles collection - stores user profile data
    match /profiles/{profileId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && 
        request.resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() && 
        resource.data.userId == request.auth.uid;
      allow delete: if false; // Soft delete only through status update
    }
    
    // Workspaces collection - stores workspace data
    match /workspaces/{workspaceId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
        request.resource.data.ownerId == request.auth.uid;
      allow update: if isAuthenticated() && 
        resource.data.ownerId == request.auth.uid;
      allow delete: if false; // Soft delete only through update
    }
    
    // Workspace members collection - stores workspace membership data
    match /workspace_members/{memberId} {
      // Allow authenticated users to read workspace memberships
      allow read: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid ||
         isWorkspaceMember(resource.data.workspaceId));
      
      // Allow creation if user is creating their own membership
      // OR if user is workspace admin adding a new member
      allow create: if isAuthenticated() && 
        (request.resource.data.userId == request.auth.uid ||
         isWorkspaceAdmin(request.resource.data.workspaceId));
      
      // Allow updates by workspace admins
      allow update: if isAuthenticated() && 
        isWorkspaceAdmin(resource.data.workspaceId);
      
      // No hard deletes allowed
      allow delete: if false;
    }
    
    // Email queue collection - for storing emails to be sent
    match /emailQueue/{document} {
      // Allow creation of emails from frontend app
      allow create: if true;
      
      // Only allow admins to read/update/delete emails
      allow read, update, delete: if request.auth != null 
        && exists(/databases/$(database)/documents/users/$(request.auth.uid)) 
        && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Integrations collection - stores API integrations for LLMs, social media, and other services
    match /integrations/{integrationId} {
      // Allow users to read their own integrations
      allow read: if isAuthenticated() && (
        // Allow reading single documents that belong to the user
        (resource != null && resource.data.userId == request.auth.uid) ||
        // Allow reading single documents available to profiles in user's workspace
        (resource != null && 
         resource.data.availableToProfiles == true &&
         resource.data.workspaceId != null &&
         isWorkspaceMember(resource.data.workspaceId)) ||
        // Allow collection queries for authenticated users
        // The server will filter the results appropriately
        request.auth != null
      );

      // Allow any authenticated user to create integrations
      // The server will set the correct userId and workspaceId
      // Supports both API key and OAuth integrations
      allow create: if request.auth != null &&
        request.resource.data.userId == request.auth.uid &&
        request.resource.data.provider is string &&
        request.resource.data.credentials is map &&
        (request.resource.data.credentials.type in ['apikey', 'oauth'] || 
         request.resource.data.credentials.apiKey is string);

      // Allow users to update their own integrations
      // Ensures OAuth token refresh and settings updates are allowed
      allow update: if request.auth != null &&
        resource.data.userId == request.auth.uid &&
        request.resource.data.userId == request.auth.uid;

      // Soft delete only - no hard deletes
      allow delete: if false;
    }
    
    // Calendar events collection - stores calendar events for users
    match /calendar_events/{eventId} {
      // Allow users to read their own events or events in their workspace
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isWorkspaceMember(resource.data.workspaceId)
      );
      
      // Allow users to create events in their workspace
      allow create: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid &&
        isWorkspaceMember(request.resource.data.workspaceId);
      
      // Allow users to update their own events
      allow update: if isAuthenticated() &&
        resource.data.userId == request.auth.uid &&
        request.resource.data.userId == request.auth.uid;
      
      // Allow users to delete their own events
      allow delete: if isAuthenticated() &&
        resource.data.userId == request.auth.uid;
    }
    
    // Calendar settings collection - stores calendar settings per user/workspace
    match /calendar_settings/{settingsId} {
      // Allow users to read their own settings
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isWorkspaceMember(resource.data.workspaceId)
      );
      
      // Allow users to create their own settings
      allow create: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid &&
        isWorkspaceMember(request.resource.data.workspaceId);
      
      // Allow users to update their own settings
      allow update: if isAuthenticated() &&
        resource.data.userId == request.auth.uid &&
        request.resource.data.userId == request.auth.uid;
      
      // Soft delete only - no hard deletes
      allow delete: if false;
    }
    
    // Calendar integrations collection - stores calendar sync integrations
    match /calendar_integrations/{integrationId} {
      // Allow users to read their own integrations
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isWorkspaceMember(resource.data.workspaceId)
      );
      
      // Allow users to create their own integrations
      allow create: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid &&
        isWorkspaceMember(request.resource.data.workspaceId);
      
      // Allow users to update their own integrations
      allow update: if isAuthenticated() &&
        resource.data.userId == request.auth.uid &&
        request.resource.data.userId == request.auth.uid;
      
      // Allow users to delete their own integrations
      allow delete: if isAuthenticated() &&
        resource.data.userId == request.auth.uid;
    }
    
    // Calendars collection - stores user-created calendars
    match /calendars/{calendarId} {
      // Allow users to read their own calendars or calendars in their workspace
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isWorkspaceMember(resource.data.workspaceId)
      );
      
      // Allow users to create calendars in their workspace
      allow create: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid &&
        isWorkspaceMember(request.resource.data.workspaceId) &&
        request.resource.data.name is string &&
        request.resource.data.color is string;
      
      // Allow users to update their own calendars
      allow update: if isAuthenticated() &&
        resource.data.userId == request.auth.uid &&
        request.resource.data.userId == request.auth.uid;
      
      // Allow users to delete their own calendars
      allow delete: if isAuthenticated() &&
        resource.data.userId == request.auth.uid;
    }
    
    // Transactions collection - stores financial transactions
    match /transactions/{transactionId} {
      // Allow users to read their own transactions or transactions in their workspace
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isWorkspaceMember(resource.data.workspaceId)
      );
      
      // Allow users to create transactions in their workspace
      allow create: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid &&
        isWorkspaceMember(request.resource.data.workspaceId);
      
      // Allow users to update their own transactions
      allow update: if isAuthenticated() &&
        resource.data.userId == request.auth.uid &&
        request.resource.data.userId == request.auth.uid;
      
      // Allow users to delete their own transactions
      allow delete: if isAuthenticated() &&
        resource.data.userId == request.auth.uid;
    }
    
    // Default rule - deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
